# Dynasty Trade Calculator - Test Reference

## Quick Test Overview
**Total**: 50 tests, 370 assertions - ALL PASSING ✅  
**Run**: `vendor/bin/phpunit` (~0.07 seconds)

## Test Files & Coverage

### 1. **BasicTest.php** (5 tests)
Basic PHP functionality validation
- `testBasicPhpFunctionality()` - Basic PHP operations (true, math, strings)
- `testDateFunctions()` - Date handling and time operations
- `testArrayOperations()` - Array count, contains operations
- `testStringOperations()` - String contains, length operations
- `testObjectCreation()` - stdClass object creation and properties

### 2. **DebugTest.php** (10 tests)
Debug utility system validation
- `testDebugModeDetection()` - DTC_DEBUG constant detection
- `testVerboseModeDetection()` - DTC_DEBUG_VERBOSE constant detection
- `testDebugStatusArray()` - Debug status array structure validation
- `testApiCallLoggingStructure()` - API call logging data structure
- `testMembershipLoggingStructure()` - Membership logging data structure
- `testForceLoggingLogic()` - Force logging override logic
- `testConstantFallbackLogic()` - DTC_DEBUG to WP_DEBUG fallback

### 3. **MembershipTest.php** (7 tests)
Core membership functionality
- `testMembershipLevelToRotoGptMapping()` - Level mapping (8→free, 7→standard_50, etc.)
- `testPilotProgramInvitationDetection()` - Pilot invitation via notes/pricing
- `testLegacyMembershipOptionsLostDetection()` - Legacy options lost detection
- `testMembershipStatusValidation()` - Status validation (active, cancelled, expired, pending)
- `testMembershipExpirationDateHandling()` - Expiration date validation
- `testCancelledMembershipValidation()` - Cancelled membership logic
- `testMembershipAtSpecificDate()` - Membership validity at specific dates

### 4. **MembershipApiTest.php** (11 tests)
RotoGPT API integration
- `testRotoGptSigninAuthentication()` - Signin request structure
- `testRotoGptCreateSubscriptionRequest()` - Create subscription requests
- `testRotoGptUpdateSubscriptionRequest()` - Update subscription requests
- `testApiEndpointSelection()` - Production vs development endpoints
- `testSuccessfulApiResponseValidation()` - Success response validation
- `testApiErrorResponseHandling()` - API error response handling
- `testHttpErrorHandling()` - HTTP timeout/error handling
- `testSubscriptionTypeValidation()` - Valid subscription types
- `testJsonEncodingDecoding()` - JSON encoding/decoding
- `testUpgradeDowngradeDetection()` - Upgrade/downgrade logic
- `testImmediateVsScheduledChanges()` - Immediate vs scheduled changes

### 5. **MembershipMockTest.php** (9 tests)
Mock-based membership testing
- Customer-membership relationships
- Special pricing detection (2.99, 29.99)
- Membership status transitions
- Membership expiration logic
- Customer notes parsing
- Date boundary conditions
- Empty/null value handling

### 6. **MembershipEdgeCasesTest.php** (8 tests)
Edge cases and data validation
- Invalid membership levels and subscription types
- Date boundary conditions and formatting edge cases
- Special characters in customer data (Unicode, HTML, SQL injection)
- JSON encoding/decoding edge cases
- Concurrent membership scenarios
- Membership upgrade/downgrade edge cases
- API timeout and retry scenarios
- Memory and performance edge cases

## Test Data Security ✅
- **Mock Data Only**: All tests use mock data (e.g., `mock_access_token_123`)
- **No Secrets**: No real wp-config data or API keys exposed
- **Git Safe**: All test files safe for version control

## Quick Test Commands
Simply run
- npm run test

- Other ways:
```bash
# Run all tests
vendor/bin/phpunit

# Run specific test file
vendor/bin/phpunit tests/MembershipTest.php

# Run specific test method
vendor/bin/phpunit --filter testMembershipLevelToRotoGptMapping

# Verbose output
vendor/bin/phpunit --verbose
```
